"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "solution_confirm",
  setup(__props) {
    const caseNumber = common_vendor.ref("");
    const workOrderData = common_vendor.ref({
      id: "MED20230001",
      case_status_cn: "进行中",
      // 修改为与图片一致的状态
      createDate: "2025-06-01"
    });
    const selectedSolutionIndex = common_vendor.ref(0);
    const solutions = common_vendor.ref([
      {
        id: 1,
        title: "方案一",
        content: "免除全部逾期利息，本金分24期等额还款",
        // 方案内容描述
        totalAmount: 5e4,
        // 还款总额
        monthlyPayment: 2083.33,
        // 月还款额
        discountAmount: 1e4,
        // 减免金额
        periods: 24
        // 还款期数
      },
      {
        id: 2,
        title: "方案二",
        content: "减免50%逾期利息，本金分36期等额还款",
        totalAmount: 55e3,
        monthlyPayment: 1527.78,
        discountAmount: 5e3,
        periods: 36
      }
    ]);
    common_vendor.computed(() => {
      return solutions.value[selectedSolutionIndex.value];
    });
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = (_a = currentPage.$page) == null ? void 0 : _a.options;
      if (options && options.case_number) {
        try {
          caseNumber.value = decodeURIComponent(options.case_number);
          common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:196", "接收到工单ID:", caseNumber.value);
          fetchSolutionDetail(caseNumber.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:199", "case_number参数解码失败:", error);
          caseNumber.value = options.case_number;
          fetchSolutionDetail(caseNumber.value);
        }
      } else {
        common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:205", "使用默认方案数据");
      }
    });
    const fetchSolutionDetail = (id) => {
      if (id) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        utils_api.api.solution.getDetail(id).then((res) => {
          common_vendor.index.hideLoading();
          if (res.code === 0) {
            workOrderData.value = res.data.workOrder;
            if (res.data.solutions && res.data.solutions.length > 0) {
              solutions.value = res.data.solutions;
            }
          } else {
            common_vendor.index.showToast({
              title: res.message || "获取方案详情失败",
              icon: "none"
            });
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/solution_confirm/solution_confirm.vue:240", "获取方案详情失败", err);
          common_vendor.index.showToast({
            title: "获取方案详情失败",
            icon: "none"
          });
        });
      } else {
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:249", "调解方案数据已加载（模拟）");
        }, 500);
      }
    };
    const selectSolution = (index) => {
      selectedSolutionIndex.value = index;
      common_vendor.index.__f__("log", "at pages/solution_confirm/solution_confirm.vue:260", "选择了方案:", solutions.value[index].title);
    };
    const viewSolutionDetail = (solution) => {
    };
    const handleConfirm = () => {
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(workOrderData.value.id || "MED20230001"),
        b: common_vendor.t(workOrderData.value.case_status_cn || "进行中"),
        c: workOrderData.value.case_status_cn === "进行中" ? 1 : "",
        d: common_vendor.t(workOrderData.value.createDate || "2023-11-01"),
        e: selectedSolutionIndex.value === 0
      }, selectedSolutionIndex.value === 0 ? {} : {}, {
        f: selectedSolutionIndex.value === 0 ? 1 : "",
        g: common_vendor.t(solutions.value[0].content),
        h: common_vendor.t(solutions.value[0].totalAmount.toFixed(2)),
        i: common_vendor.t(solutions.value[0].monthlyPayment.toFixed(2)),
        j: common_vendor.t(solutions.value[0].discountAmount.toFixed(2)),
        k: common_vendor.o(($event) => viewSolutionDetail(solutions.value[0])),
        l: selectedSolutionIndex.value === 0 ? 1 : "",
        m: common_vendor.o(($event) => selectSolution(0)),
        n: selectedSolutionIndex.value === 1
      }, selectedSolutionIndex.value === 1 ? {} : {}, {
        o: selectedSolutionIndex.value === 1 ? 1 : "",
        p: common_vendor.t(solutions.value[1].content),
        q: common_vendor.t(solutions.value[1].totalAmount.toFixed(2)),
        r: common_vendor.t(solutions.value[1].monthlyPayment.toFixed(2)),
        s: common_vendor.t(solutions.value[1].discountAmount.toFixed(2)),
        t: common_vendor.o(($event) => viewSolutionDetail(solutions.value[1])),
        v: selectedSolutionIndex.value === 1 ? 1 : "",
        w: common_vendor.o(($event) => selectSolution(1)),
        x: common_vendor.t(solutions.value[selectedSolutionIndex.value].title),
        y: common_vendor.o(handleConfirm)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5489ef86"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/solution_confirm/solution_confirm.js.map
