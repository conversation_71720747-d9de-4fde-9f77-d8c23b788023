{"version": 3, "file": "solution_confirm.js", "sources": ["pages/solution_confirm/solution_confirm.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc29sdXRpb25fY29uZmlybS9zb2x1dGlvbl9jb25maXJtLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"solution-confirm-container\">\r\n\t\t<!-- 工单基本信息 -->\r\n\t\t<view class=\"work-order-card\">\r\n\t\t\t<view class=\"work-order-header\">\r\n\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t<text>调解案件号: </text>\r\n\t\t\t\t\t<text class=\"work-order-id\">{{workOrderData.id || 'MED20230001'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"work-order-status\">\r\n\t\t\t\t\t<text class=\"status-label\" :class=\"{'status-processing': workOrderData.case_status_cn === '进行中'}\">{{workOrderData.case_status_cn || '进行中'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"work-order-date\">发起日期: {{workOrderData.createDate || '2023-11-01'}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 进度条 -->\r\n\t\t<view class=\"progress-bar\">\r\n\t\t\t<view class=\"progress-steps\">\r\n\t\t\t\t<view class=\"progress-step completed\">\r\n\t\t\t\t\t<view class=\"step-circle\">1</view>\r\n\t\t\t\t\t<view class=\"step-line completed\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">调解确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step active\">\r\n\t\t\t\t\t<view class=\"step-circle\">2</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">方案确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">3</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">协议签署</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">4</view>\r\n\t\t\t\t\t<view class=\"step-label\">完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 方案列表 -->\r\n\t\t<view class=\"solutions-container\">\r\n\t\t\t<!-- 方案一 -->\r\n\t\t\t<view \r\n\t\t\t\tclass=\"solution-card\" \r\n\t\t\t\t:class=\"{'selected': selectedSolutionIndex === 0}\"\r\n\t\t\t\t@click=\"selectSolution(0)\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"solution-header\">\r\n\t\t\t\t\t<view class=\"solution-title-wrap\">\r\n\t\t\t\t\t\t<text class=\"solution-title\">方案一</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"solution-select\">\r\n\t\t\t\t\t\t<view class=\"radio-button\" :class=\"{'selected': selectedSolutionIndex === 0}\">\r\n\t\t\t\t\t\t\t<view class=\"radio-inner\" v-if=\"selectedSolutionIndex === 0\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"solution-content\">\r\n\t\t\t\t\t<text class=\"solution-label\">方案内容</text>\r\n\t\t\t\t\t<text>{{solutions[0].content}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"solution-details\">\r\n\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t<text class=\"solution-label\">还款总额</text>\r\n\t\t\t\t\t\t<text class=\"solution-value\">¥{{solutions[0].totalAmount.toFixed(2)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t<text class=\"solution-label\">月还款额</text>\r\n\t\t\t\t\t\t<text class=\"solution-value\">¥{{solutions[0].monthlyPayment.toFixed(2)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"solution-footer\">\r\n\t\t\t\t\t<text class=\"discount-amount\">减免金额: ¥{{solutions[0].discountAmount.toFixed(2)}}</text>\r\n\t\t\t\t\t<text class=\"view-detail\" @click.stop=\"viewSolutionDetail(solutions[0])\">查看详情</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 方案二 -->\r\n\t\t\t<view \r\n\t\t\t\tclass=\"solution-card\" \r\n\t\t\t\t:class=\"{'selected': selectedSolutionIndex === 1}\"\r\n\t\t\t\t@click=\"selectSolution(1)\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"solution-header\">\r\n\t\t\t\t\t<view class=\"solution-title-wrap\">\r\n\t\t\t\t\t\t<text class=\"solution-title\">方案二</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"solution-select\">\r\n\t\t\t\t\t\t<view class=\"radio-button\" :class=\"{'selected': selectedSolutionIndex === 1}\">\r\n\t\t\t\t\t\t\t<view class=\"radio-inner\" v-if=\"selectedSolutionIndex === 1\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"solution-content\">\r\n\t\t\t\t\t<text class=\"solution-label\">方案内容</text>\r\n\t\t\t\t\t<text>{{solutions[1].content}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"solution-details\">\r\n\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t<text class=\"solution-label\">还款总额</text>\r\n\t\t\t\t\t\t<text class=\"solution-value\">¥{{solutions[1].totalAmount.toFixed(2)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t<text class=\"solution-label\">月还款额</text>\r\n\t\t\t\t\t\t<text class=\"solution-value\">¥{{solutions[1].monthlyPayment.toFixed(2)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"solution-footer\">\r\n\t\t\t\t\t<text class=\"discount-amount\">减免金额: ¥{{solutions[1].discountAmount.toFixed(2)}}</text>\r\n\t\t\t\t\t<text class=\"view-detail\" @click.stop=\"viewSolutionDetail(solutions[1])\">查看详情</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<button class=\"confirm-button\" @click=\"handleConfirm\">确认选择{{solutions[selectedSolutionIndex].title}}</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 添加联系客服悬浮按钮 -->\r\n\t\t<!-- <contact-fab></contact-fab> -->\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { api } from '@/utils/api.js';\r\n// import ContactFab from '@/components/contact-fab/contact-fab.vue';\r\n\r\n/**\r\n * 调解方案确认页面\r\n * \r\n * 此页面用于展示和选择多个调解方案，支持方案对比和确认功能\r\n * 用户可以查看方案详情，选择方案并确认选择\r\n */\r\n\r\n// 接收页面参数 - 工单ID\r\nconst caseNumber = ref('');\r\n\r\n// 工单基本数据\r\nconst workOrderData = ref({\r\n\tid: 'MED20230001',\r\n\tcase_status_cn: '进行中', // 修改为与图片一致的状态\r\n\tcreateDate: '2025-06-01'\r\n});\r\n\r\n// 选中的方案索引（默认选中方案一）\r\nconst selectedSolutionIndex = ref(0);\r\n\r\n// 调解方案数组 - 包含多个方案\r\nconst solutions = ref([\r\n\t{\r\n\t\tid: 1,\r\n\t\ttitle: '方案一',\r\n\t\tcontent: '免除全部逾期利息，本金分24期等额还款', // 方案内容描述\r\n\t\ttotalAmount: 50000.00, // 还款总额\r\n\t\tmonthlyPayment: 2083.33, // 月还款额\r\n\t\tdiscountAmount: 10000.00, // 减免金额\r\n\t\tperiods: 24 // 还款期数\r\n\t},\r\n\t{\r\n\t\tid: 2,\r\n\t\ttitle: '方案二',\r\n\t\tcontent: '减免50%逾期利息，本金分36期等额还款',\r\n\t\ttotalAmount: 55000.00,\r\n\t\tmonthlyPayment: 1527.78,\r\n\t\tdiscountAmount: 5000.00,\r\n\t\tperiods: 36\r\n\t}\r\n]);\r\n\r\n// 计算当前选中的方案\r\nconst selectedSolution = computed(() => {\r\n\treturn solutions.value[selectedSolutionIndex.value];\r\n});\r\n\r\n// 生命周期钩子 - 页面加载时执行\r\nonMounted(() => {\r\n\t// 获取页面参数\r\n\tconst pages = getCurrentPages();\r\n\tconst currentPage = pages[pages.length - 1];\r\n\tconst options = currentPage.$page?.options;\r\n\r\n\tif (options && options.case_number) {\r\n\t\ttry {\r\n\t\t\t// URL解码处理中文参数\r\n\t\t\tcaseNumber.value = decodeURIComponent(options.case_number);\r\n\t\t\tconsole.log('接收到工单ID:', caseNumber.value);\r\n\t\t\tfetchSolutionDetail(caseNumber.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('case_number参数解码失败:', error);\r\n\t\t\tcaseNumber.value = options.case_number;\r\n\t\t\tfetchSolutionDetail(caseNumber.value);\r\n\t\t}\r\n\t} else {\r\n\t\t// 使用默认数据\r\n\t\tconsole.log('使用默认方案数据');\r\n\t}\r\n});\r\n\r\n/**\r\n * 获取方案详情数据\r\n * @param {String} id - 工单ID\r\n */\r\nconst fetchSolutionDetail = (id) => {\r\n\tif (id) {\r\n\t\t// 使用API获取数据\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '加载中...'\r\n\t\t});\r\n\t\t\r\n\t\tapi.solution.getDetail(id)\r\n\t\t\t.then(res => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t// 设置工单基本信息\r\n\t\t\t\t\tworkOrderData.value = res.data.workOrder;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果API返回了多个方案，则更新方案数组\r\n\t\t\t\t\tif (res.data.solutions && res.data.solutions.length > 0) {\r\n\t\t\t\t\t\tsolutions.value = res.data.solutions;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.message || '获取方案详情失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch(err => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('获取方案详情失败', err);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取方案详情失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t} else {\r\n\t\t// 使用模拟数据\r\n\t\tsetTimeout(() => {\r\n\t\t\tconsole.log('调解方案数据已加载（模拟）');\r\n\t\t}, 500);\r\n\t}\r\n};\r\n\r\n/**\r\n * 选择方案\r\n * @param {Number} index - 方案索引\r\n */\r\nconst selectSolution = (index) => {\r\n\tselectedSolutionIndex.value = index;\r\n\tconsole.log('选择了方案:', solutions.value[index].title);\r\n};\r\n\r\n/**\r\n * 查看方案详情\r\n * @param {Object} solution - 方案对象\r\n */\r\nconst viewSolutionDetail = (solution) => {\r\n\t/* uni.showModal({\r\n\t\ttitle: `${solution.title}详情`,\r\n\t\tcontent: `${solution.content}\\n\\n还款总额: ¥${solution.totalAmount.toFixed(2)}\\n月还款额: ¥${solution.monthlyPayment.toFixed(2)}\\n还款期数: ${solution.periods}期\\n减免金额: ¥${solution.discountAmount.toFixed(2)}`,\r\n\t\tshowCancel: false\r\n\t}); */\r\n};\r\n\r\n/**\r\n * 处理确认方案\r\n * 确认用户选择的方案并提交到服务器\r\n */\r\nconst handleConfirm = () => {\r\n\t/* const selectedSolution = solutions.value[selectedSolutionIndex.value];\r\n\t\r\n\t// 显示确认对话框\r\n\tuni.showModal({\r\n\t\ttitle: '确认方案',\r\n\t\tcontent: `您确定要选择${selectedSolution.title}吗？`,\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 用户点击确定\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 调用API确认方案\r\n\t\t\t\tif (caseNumber.value) {\r\n\t\t\t\t\tapi.solution.confirmSolution(caseNumber.value, { solutionId: selectedSolution.id })\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '方案已确认',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 成功后跳转到首页\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/agreement_signing/agreement_signing',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到协议签署页面');\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.message || '操作失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tconsole.error('确认方案失败', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '确认方案失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 模拟API调用\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '方案已确认',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t// 延迟跳转到首页\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/agreement_signing/agreement_signing',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到协议签署页面');\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}); */\r\n};\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:root {\r\n\t--primary-color: #3b7eeb;\r\n}\r\n.solution-confirm-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n/* 工单卡片样式 */\r\n.work-order-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.work-order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n\tpadding: 6rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n\tfont-size: 26rpx;\r\n\tpadding: 8rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: #fff;\r\n}\r\n\r\n.status-processing {\r\n\tbackground-color: #1890ff;\r\n\tcolor: #fff;\r\n}\r\n\r\n.work-order-date {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 进度条样式 */\r\n/* .progress-bar {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n} */\r\n\r\n.progress-steps {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-step {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tposition: relative;\r\n}\r\n\r\n.step-circle {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #e0e0e0;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.step-line {\r\n\tposition: absolute;\r\n\ttop: 30rpx;\r\n\tleft: 50%;\r\n\tright: -50%;\r\n\theight: 4rpx;\r\n\tbackground-color: #e0e0e0;\r\n\tz-index: 1;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n\tdisplay: none;\r\n}\r\n\r\n.step-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\ttext-align: center;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.progress-step.active .step-label {\r\n\tcolor: #2979ff;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.progress-step.completed .step-circle {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.step-line.completed {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.progress-step.completed .step-label {\r\n\tcolor: #2979ff;\r\n}\r\n\r\n/* 方案容器 */\r\n.solutions-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n/* 方案卡片样式 */\r\n.solution-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tborder: 2rpx solid #eee;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.solution-card.selected {\r\n\tborder-color: #2979ff;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(41, 121, 255, 0.1);\r\n}\r\n\r\n.solution-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.solution-title-wrap {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.solution-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.solution-tag {\r\n\tfont-size: 24rpx;\r\n\tcolor: #2979ff;\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n.solution-select {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.radio-button {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tborder-radius: 50%;\r\n\tborder: 2rpx solid rgb(221, 221, 221);\r\n\tborder-image: initial;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.radio-button.selected {\r\n\tborder-color: #3b7eeb;\r\n\tbackground-color: var(--primary-color);\r\n}\r\n\r\n.radio-inner {\r\n\twidth: 16rpx;\r\n\theight: 16rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.solution-content {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 30rpx;\r\n\tline-height: 1.5;\r\n\tgap: 16rpx;\r\n\tdisplay: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.solution-details {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.solution-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tpadding: 16rpx 0;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.solution-label {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.solution-value {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.solution-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding-top: 20rpx;\r\n\tborder-top: 1px solid #eee;\r\n}\r\n\r\n.discount-amount {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.view-detail {\r\n\tfont-size: 26rpx;\r\n\tcolor: #2979ff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n.view-detail::before {\r\n\tcontent: 'i';\r\n\tdisplay: inline-block;\r\n\twidth: 30rpx;\r\n\theight: 30rpx;\r\n\tline-height: 30rpx;\r\n\ttext-align: center;\r\n\tfont-size: 22rpx;\r\n\tfont-style: italic;\r\n\tfont-weight: bold;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #2979ff;\r\n\tcolor: #fff;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n/* 联系客服区域 */\r\n.contact-section {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.contact-button {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 10rpx;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 12rpx;\r\n\tbackground-color: #fff;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.contact-button.phone {\r\n\tcolor: #2979ff;\r\n}\r\n\r\n.contact-button.wechat {\r\n\tcolor: #07c160;\r\n}\r\n\r\n.contact-button .fa,\r\n.contact-button .fab {\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n/* 底部按钮 */\r\n.action-buttons {\r\n\tmargin-top: 40rpx;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.confirm-button {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\ttext-align: center;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 32rpx;\r\n\tbackground-color: #2979ff;\r\n\tcolor: #fff;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/solution_confirm/solution_confirm.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "uni", "api"], "mappings": ";;;;;;AAiJA,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACzB,IAAI;AAAA,MACJ,gBAAgB;AAAA;AAAA,MAChB,YAAY;AAAA,IACb,CAAC;AAGD,UAAM,wBAAwBA,cAAAA,IAAI,CAAC;AAGnC,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACrB;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA;AAAA,QACT,aAAa;AAAA;AAAA,QACb,gBAAgB;AAAA;AAAA,QAChB,gBAAgB;AAAA;AAAA,QAChB,SAAS;AAAA;AAAA,MACT;AAAA,MACD;AAAA,QACC,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAGwBC,kBAAAA,SAAS,MAAM;AACvC,aAAO,UAAU,MAAM,sBAAsB,KAAK;AAAA,IACnD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;;AAEf,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,WAAU,iBAAY,UAAZ,mBAAmB;AAEnC,UAAI,WAAW,QAAQ,aAAa;AACnC,YAAI;AAEH,qBAAW,QAAQ,mBAAmB,QAAQ,WAAW;AACzDC,wBAAY,MAAA,MAAA,OAAA,sDAAA,YAAY,WAAW,KAAK;AACxC,8BAAoB,WAAW,KAAK;AAAA,QACpC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,sBAAsB,KAAK;AACzC,qBAAW,QAAQ,QAAQ;AAC3B,8BAAoB,WAAW,KAAK;AAAA,QACpC;AAAA,MACH,OAAQ;AAENA,sBAAAA,MAAA,MAAA,OAAA,sDAAY,UAAU;AAAA,MACtB;AAAA,IACF,CAAC;AAMD,UAAM,sBAAsB,CAAC,OAAO;AACnC,UAAI,IAAI;AAEPA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACV,CAAG;AAEDC,sBAAI,SAAS,UAAU,EAAE,EACvB,KAAK,SAAO;AACZD,wBAAG,MAAC,YAAW;AACf,cAAI,IAAI,SAAS,GAAG;AAEnB,0BAAc,QAAQ,IAAI,KAAK;AAG/B,gBAAI,IAAI,KAAK,aAAa,IAAI,KAAK,UAAU,SAAS,GAAG;AACxD,wBAAU,QAAQ,IAAI,KAAK;AAAA,YAC3B;AAAA,UACN,OAAW;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACZ,CAAM;AAAA,UACD;AAAA,QACL,CAAI,EACA,MAAM,SAAO;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,GAAG;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACX,CAAK;AAAA,QACL,CAAI;AAAA,MACJ,OAAQ;AAEN,mBAAW,MAAM;AAChBA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,eAAe;AAAA,QAC3B,GAAE,GAAG;AAAA,MACN;AAAA,IACF;AAMA,UAAM,iBAAiB,CAAC,UAAU;AACjC,4BAAsB,QAAQ;AAC9BA,6FAAY,UAAU,UAAU,MAAM,KAAK,EAAE,KAAK;AAAA,IACnD;AAMA,UAAM,qBAAqB,CAAC,aAAa;AAAA,IAMzC;AAMA,UAAM,gBAAgB,MAAM;AAAA,IA0F5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/WA,GAAG,WAAW,eAAe;"}