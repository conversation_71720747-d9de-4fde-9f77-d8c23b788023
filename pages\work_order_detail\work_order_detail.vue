<template>
	<view class="work-order-detail-container">
		<!-- 案件基本信息 -->
		<view class="work-order-card">
			<view class="work-order-header">
				<view class="work-order-title">
					<text>调解案件号: </text>
					<text class="work-order-id">{{caseNumber}}</text>
				</view>
				<view class="work-order-status">
					<text class="status-label" :class="{'status-pending': caseStatus === '待确认'}">{{caseStatus}}</text>
				</view>
			</view>
			<view class="work-order-date">发起日期: {{initiateDate}}</view>
			<view class="work-order-date" v-if="caseStatus === '已关闭'">关闭日期: {{closeDate}}</view>
		</view>
		
		<!-- 进度条 -->
		<view class="progress-bar">
			<view class="progress-steps">
				<view class="progress-step active">
					<view class="step-circle">1</view>
					<view class="step-line"></view>
					<view class="step-label">调解确认</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">2</view>
					<view class="step-line"></view>
					<view class="step-label">方案确认</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">3</view>
					<view class="step-line"></view>
					<view class="step-label">协议签署</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">4</view>
					<view class="step-label">完成</view>
				</view>
			</view>
		</view>
		
		<!-- 调解信息 -->
		<view class="info-section">
			<view class="section-title">调解信息</view>
			<view class="info-item" v-for="item in workOrderData.mediation_config" :key="item.id">
				<text class="info-label">{{item.title}}</text>
				<text class="info-value">{{item.value}}</text>
			</view>
		</view>
		<!-- 文件列表 -->
		<view class="info-section">
			<view class="section-title">相关文件</view>
			<view class="file-item" v-for="file in workOrderData.attachments" :key="file.id">
				<!-- 根据file.name的扩展名,判断是pdf还是图片 -->
				<i class="fas " :class="file.name.split('.').pop() === 'pdf' ? 'fa-file-pdf' : 'fa-file-image'" :style="{color: file.name.split('.').pop() === 'pdf' ? '#ff4d4f' : '#52c41a'}"></i>
				<view class="file-content"><text class="file-name">{{file.name}}</text></view>
			</view>
		</view>

		<view class="info-section work-closing" v-if="workOrderData.case_status_cn === '已关闭'">
			<view class="section-title">
				<i class="fas fa-info-circle"></i>关闭原因
			</view>
			<view class="section-tip">
				<text>{{workOrderData.closingReason || '调解案件已超过规定期限。'}}</text>
			</view>
		</view>
		<!-- 底部操作按钮 -->
		<view class="action-buttons" v-else>
			<button class="accept-button" @click="handleAccept"><i class="fas fa-check-circle"></i>接受调解</button>
			<button class="reject-button" @click="handleReject"><i class="fas fa-clock"></i>考虑一下</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { api } from '@/utils/api.js';

// 接收参数
const caseNumber = ref('');
const initiateDate = ref('');
const closeDate = ref('');
const caseStatus = ref('');

// 调解数据
const workOrderData = ref([]);

// 页面加载时获取参数 - uni-app标准方式
// onLoad是uni-app专门用于接收页面参数的生命周期钩子
onLoad((options) => {
	console.log('页面参数:', options);

	// 获取调解案件ID参数
	if (options.case_number) {
		try {
			// URL解码处理中文参数
			caseNumber.value = decodeURIComponent(options.case_number);
			console.log('接收到调解ID:', caseNumber.value);
		} catch (error) {
			console.error('case_number参数解码失败:', error);
			caseNumber.value = options.case_number;
		}
	}
	// 获取创建日期参数
	if (options.initiate_date) {
		try {
			// URL解码处理中文参数
			initiateDate.value = decodeURIComponent(options.initiate_date);
			console.log('接收到日期:', initiateDate.value);
		} catch (error) {
			console.error('initiate_date参数解码失败:', error);
			initiateDate.value = options.initiate_date;
		}
	}

	// 获取关闭日期参数
	if (options.close_date) {
		try {
			// URL解码处理中文参数
			closeDate.value = decodeURIComponent(options.close_date);
			console.log('关闭日期:', closeDate.value);
		} catch (error) {
			console.error('close_date参数解码失败:', error);
			closeDate.value = options.close_date;
		}
	}

	// 获取调解状态参数
	if (options.case_status_cn) {
		try {
			// URL解码处理中文参数
			caseStatus.value = decodeURIComponent(options.case_status_cn);
			console.log('接收到调解状态:', caseStatus.value);

			// 只有当status参数为"已关闭"时，才将其赋值给workOrderData.case_status_cn
			// 这样可以确保已关闭的案件能正确显示关闭状态和相关信息
			if (caseStatus.value === '已关闭') {
				// 将URL传递的状态赋值给工单数据的状态字段
				workOrderData.value.case_status_cn = caseStatus.value;
				console.log('检测到已关闭状态，已更新workOrderData状态为:', caseStatus.value);
			}
		} catch (error) {
			console.error('case_status_cn参数解码失败:', error);
			caseStatus.value = options.case_status_cn;
		}
	}

	// 获取详细的工单数据
	if (caseNumber.value) {
		fetchWorkOrderDetail(caseNumber.value);
	}
});

// 组件挂载后的初始化逻辑
onMounted(() => {
	console.log('工单详情页面组件已挂载');
	// 注意：参数获取逻辑已移至onLoad中，这里只保留组件挂载后的其他初始化操作
});

// 获取调解确认数据
const fetchWorkOrderDetail = (id) => {
	if (id) {
		// 使用API获取数据
		uni.showLoading({
			title: '加载中...'
		});
		// mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递
		api.workOrder.getDetail(id)
			.then(res => {
				uni.hideLoading();
				if (res.state === "success") {
					workOrderData.value = res.data;
				} else {
					uni.showToast({
						title: res.msg || '获取调解确认失败',
						icon: 'none'
					});
				}
			})
			.catch(err => {
				uni.hideLoading();
				console.error('获取调解确认失败', err);
				uni.showToast({
					title: '获取调解确认失败',
					icon: 'none'
				});
			});
	}
};

// 处理接受调解
const handleAccept = () => {
	// 显示确认对话框
	uni.showModal({
		title: '确认接受',
		content: '您确定要接受此调解吗？',
		success: (res) => {
			if (res.confirm) {
				// 用户点击确定
				uni.showLoading({
					title: '处理中...'
				});
				
				// 调用API接受调解
				if (caseNumber.value) {
					api.workOrder.acceptWorkOrder(caseNumber.value)
						.then(res => {
							uni.hideLoading();
							if (res.state === "success") {
								uni.showToast({
									title: res.msg,
									icon: 'success',
									duration: 1500
								});
								
								// 成功后跳转到调解方案确认页面
								setTimeout(() => {
									uni.navigateTo({
										url: `/pages/solution_confirm/solution_confirm?case_number=${res.data.case_number}`,
										success: () => {
											console.log('跳转到调解方案确认页面');
										},
										fail: (err) => {
											console.error('跳转失败', err);
											uni.showToast({
												title: '跳转失败',
												icon: 'none'
											});
										}
									});
								}, 1500);
							} else {
								uni.showToast({
									title: res.msg || '操作失败',
									icon: 'none'
								});
							}
						})
						.catch(err => {
							uni.hideLoading();
							console.error('接受调解失败', err);
							uni.showToast({
								title: '接受调解失败',
								icon: 'none'
							});
						});
				}else{
					uni.showToast({
						title: '缺少调解案件编号',
						icon: 'none'
					});
				}
			}
		}
	});
};

// 考虑一下
const handleReject = () => {
	uni.redirectTo({
		url: `/pages/mediation_query/mediation_query`,
	});
	// 显示确认对话框
	/* uni.showModal({
		title: '确认拒绝',
		content: '您确定要拒绝此调解吗？',
		success: (res) => {
			if (res.confirm) {
				// 用户点击确定
				uni.showLoading({
					title: '处理中...'
				});
				
				// 调用API拒绝调解
				if (caseNumber.value) {
					api.workOrder.rejectWorkOrder(caseNumber.value)
						.then(res => {
							uni.hideLoading();
							if (res.code === 0) {
								uni.showToast({
									title: '调解已拒绝',
									icon: 'success',
									duration: 1500
								});
								
								// 成功后跳转到首页
								setTimeout(() => {
									uni.switchTab({
										url: '/pages/index/index',
										success: () => {
											console.log('跳转到首页');
										},
										fail: (err) => {
											console.error('跳转失败', err);
											uni.showToast({
												title: '跳转失败',
												icon: 'none'
											});
										}
									});
								}, 1500);
							} else {
								uni.showToast({
									title: res.msg || '操作失败',
									icon: 'none'
								});
							}
						})
						.catch(err => {
							uni.hideLoading();
							console.error('拒绝调解失败', err);
							uni.showToast({
								title: '拒绝调解失败',
								icon: 'none'
							});
						});
				}
			}
		}
	}); */
};


</script>

<style lang="scss">
.work-order-detail-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.work-order-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.work-order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.work-order-title {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
}

.work-order-status {
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
}

.status-label {
	font-size: 26rpx;
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	background-color: #999;
	color: #fff;
}

.status-pending {
	background-color: #faad14;
}

.work-order-date {
	font-size: 28rpx;
	color: #666;
}

/* .progress-bar {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
} */

.progress-steps {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
}

.step-circle {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #e0e0e0;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	position: relative;
	z-index: 2;
}

.step-line {
	position: absolute;
	top: 30rpx;
	left: 50%;
	right: -50%;
	height: 4rpx;
	background-color: #e0e0e0;
	z-index: 1;
}

.progress-step:last-child .step-line {
	display: none;
}

.step-label {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

.progress-step.active .step-circle {
	background-color: #2979ff;
}

.progress-step.active .step-label {
	color: #2979ff;
	font-weight: bold;
}

.info-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	position: relative;
}

/* .section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 8rpx;
	height: 32rpx;
	background-color: #2979ff;
	border-radius: 4rpx;
} */

.info-item {
	display: flex;
	flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
	padding: 20rpx 0;
	color: #333;
	font-size: 32rpx;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-weight: 500;
}

.file-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 28rpx 0;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
    transition: 0.3s;
	.fas{
		margin-right: 24rpx;
	}
}
.file-item:last-child {
    border-bottom: none;
}
.file-content {
    min-width: 0;
    display: flex;
    align-items: center;
    flex: 1 1 0%;
}
.file-name {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    line-height: 1.3;
    word-break: break-all;
    transition: color 0.3s;
}

.action-buttons {
	display: flex;
	gap: 30rpx;
}

.reject-button,
.accept-button {
	flex: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	border-radius: 16rpx;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;
	font-size: 32rpx;
}

.reject-button {
	background-color: #fff;
	color: #3b7eeb;
	border: 2rpx solid #3b7eeb;
}

.accept-button {
	background-color: #3b7eeb;
	color: #fff;
}
.work-closing{
	background-color: rgb(255, 248, 248);
    border:2rpx solid rgb(255, 237, 237);
	.section-title{
		font-size: 36rpx;
		color:#f5222d;
		font-weight: bold;
		display: flex;
		align-items: center;
		.fas{
			color: #f5222d;
			font-size: 40rpx;
			margin-right: 20rpx;
		}
	}
	.section-tip{
		font-size: 32rpx;
		color:#666;
	}
}
</style> 